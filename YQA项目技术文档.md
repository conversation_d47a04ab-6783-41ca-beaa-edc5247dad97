# YQA/StarPro社区项目技术文档

## 更新日期：2025年1月

## 目录
- [1. 项目概述](#1-项目概述)
- [2. 功能分析](#2-功能分析)
- [3. 技术架构](#3-技术架构)
- [4. 技术栈分析](#4-技术栈分析)
- [5. 项目结构](#5-项目结构)
- [6. API接口文档](#6-api接口文档)
- [7. 数据库设计](#7-数据库设计)
- [8. 前端架构](#8-前端架构)
- [9. 后端架构](#9-后端架构)
- [10. 管理端功能](#10-管理端功能)
- [11. 部署配置](#11-部署配置)
- [12. 二次开发指南](#12-二次开发指南)
- [13. 插件系统](#13-插件系统)
- [14. 安全机制](#14-安全机制)
- [15. 性能优化](#15-性能优化)
- [16. 核心变量和函数](#16-核心变量和函数)
- [17. 开发环境搭建](#17-开发环境搭建)
- [18. 常见问题解决](#18-常见问题解决)

## 1. 项目概述

YQA（StarPro社区）是一个基于现代技术栈构建的综合性社区平台，包含论坛、商城、内容管理等功能。项目采用前后端分离架构，支持多端部署。

### 1.1 项目特点
- **多端支持**：支持H5、APP（Android/iOS）、小程序（微信）等多个平台
- **功能丰富**：集成论坛、商城、支付、内容管理、社交、GPT等功能
- **技术先进**：采用Spring Boot + uni-app + PHP管理端的技术架构
- **扩展性强**：支持插件系统，便于功能扩展
- **主题系统**：支持多种UI风格切换
- **安全可靠**：内置多重安全防护机制

### 1.2 版本信息
- **API版本**：2.6
- **APP版本**：3.1.3  
- **管理端版本**：2.6
- **框架版本**：Spring Boot 2.2.11

### 1.3 项目信息
- **开发者**：森云
- **项目名称**：StarPro
- **应用ID**：__UNI__670A143

## 2. 功能分析

### 2.1 核心功能模块

YQA项目是一个综合性社区平台，包含以下核心功能模块：

#### 2.1.1 用户系统
- **用户注册/登录**：支持邮箱、手机号、第三方登录
- **用户资料管理**：头像、昵称、个人简介、背景图等
- **权限管理**：游客、普通用户、VIP用户、管理员等级别
- **积分系统**：签到、发布内容、互动获得积分
- **VIP系统**：VIP购买、特权功能、折扣优惠
- **邀请系统**：邀请码生成、邀请奖励机制
- **实名认证**：个人认证、企业认证功能

#### 2.1.2 内容管理系统
- **文章发布**：支持富文本编辑、Markdown编辑
- **内容分类**：多级分类、标签系统
- **内容审核**：自动审核、人工审核机制
- **内容推荐**：基于算法的内容推荐
- **内容搜索**：全文搜索、分类搜索
- **内容统计**：浏览量、点赞数、评论数统计
- **付费内容**：付费文章、会员专享内容

#### 2.1.3 论坛系统
- **版块管理**：多级版块、版主管理
- **帖子发布**：支持图片、视频、链接
- **帖子管理**：置顶、推荐、锁定、删除
- **互动功能**：点赞、评论、转发、收藏
- **版主功能**：帖子审核、用户管理
- **积分奖励**：发帖、回帖获得积分

#### 2.1.4 商城系统
- **商品管理**：商品发布、分类、库存管理
- **商品类型**：实体商品、虚拟商品、教程、源码
- **购买流程**：加购物车、立即购买、订单管理
- **支付集成**：支付宝、微信支付、积分支付
- **订单管理**：订单状态、物流跟踪
- **评价系统**：商品评价、评分系统

#### 2.1.5 消息系统
- **私信功能**：用户间私信交流
- **系统通知**：系统消息推送
- **评论通知**：评论、回复通知
- **关注通知**：关注用户动态通知
- **推送服务**：APP消息推送

#### 2.1.6 社交功能
- **关注系统**：关注用户、查看动态
- **动态发布**：发布个人动态、分享内容
- **互动功能**：点赞、评论、转发
- **好友系统**：添加好友、好友列表
- **群组功能**：创建群组、群组交流

### 2.2 功能特色

#### 2.2.1 多端适配
- **响应式设计**：适配PC、平板、手机
- **APP支持**：原生APP体验
- **小程序支持**：微信、支付宝小程序
- **PWA支持**：渐进式Web应用

#### 2.2.2 智能推荐
- **内容推荐**：基于用户行为的智能推荐
- **用户推荐**：推荐感兴趣的用户
- **标签推荐**：智能标签推荐
- **搜索建议**：智能搜索建议

#### 2.2.3 安全防护
- **内容审核**：敏感词过滤、图片审核
- **防刷机制**：防止恶意刷赞、刷评论
- **IP限制**：IP黑名单、频率限制
- **数据加密**：敏感数据加密存储

#### 2.2.4 运营工具
- **数据统计**：用户活跃度、内容统计
- **广告系统**：广告位管理、广告投放
- **活动系统**：签到活动、任务系统
- **营销工具**：优惠券、促销活动

### 2.3 业务流程

#### 2.3.1 用户注册流程
```
用户访问 → 选择注册方式 → 填写信息 → 验证码验证 → 邮箱/手机验证 → 注册成功 → 完善资料
```

#### 2.3.2 内容发布流程
```
用户登录 → 选择发布类型 → 编辑内容 → 选择分类/标签 → 设置权限 → 提交审核 → 审核通过 → 发布成功
```

#### 2.3.3 商品购买流程
```
浏览商品 → 查看详情 → 加入购物车/立即购买 → 确认订单 → 选择支付方式 → 支付成功 → 订单完成
```

#### 2.3.4 论坛互动流程
```
浏览帖子 → 查看详情 → 点赞/评论/转发 → 关注作者 → 收藏帖子 → 分享给好友
```

### 2.4 权限体系

#### 2.4.1 用户权限等级
- **游客（-2）**：仅可浏览公开内容
- **注册用户（-1）**：可发布内容、评论互动
- **普通用户（0）**：完整的基础功能
- **VIP用户（1）**：享受VIP特权功能
- **管理员（2）**：拥有管理权限

#### 2.4.2 功能权限控制
- **内容发布**：需要登录用户权限
- **评论互动**：需要注册用户权限
- **商品购买**：需要实名认证
- **高级功能**：需要VIP权限
- **管理功能**：需要管理员权限

### 2.5 详细功能实现

#### 2.5.1 用户系统详细功能
- **登录方式**：
  - 邮箱密码登录（userLogin）
  - 手机号验证码登录（phoneLogin）
  - 第三方API登录（apiLogin）
  - 忘记密码重置（userFoget、userPhoneFoget）
- **注册功能**：
  - 邮箱注册（userRegister）
  - 手机号注册（userRegisterByPhone）
  - 邀请码注册（madeInvitation、invitationList）
- **用户资料**：
  - 个人信息编辑（userEdit）
  - 头像上传（upload）
  - 背景图设置
  - 个人简介管理
- **积分系统**：
  - 签到获得积分（clock）
  - 发布内容获得积分
  - 邀请用户获得积分
  - 积分消费记录（orderList）
- **VIP系统**：
  - VIP套餐购买（buyVIP、buyVIPpackage）
  - VIP特权功能
  - VIP到期提醒

#### 2.5.2 内容管理详细功能
- **文章管理**：
  - 文章发布（contentsAdd）
  - 文章编辑（contentsEdit）
  - 文章删除（contentsDelete）
  - 文章审核（contentsReview）
  - 文章置顶（contentsTop）
  - 文章推荐（contentsRecommend）
- **分类系统**：
  - 多级分类管理
  - 标签系统（setFields）
  - 自定义字段配置（contentConfig）
- **内容互动**：
  - 点赞功能（contentsLikes）
  - 评论功能（comments）
  - 收藏功能（favorites）
  - 分享功能
- **内容统计**：
  - 浏览量统计（views）
  - 点赞数统计（likes）
  - 评论数统计（commentsNum）
  - 打赏记录（rewardList）

#### 2.5.3 论坛系统详细功能
- **版块管理**：
  - 版块创建（addSection）
  - 版块编辑（editSection）
  - 版块删除（deleteSection）
  - 版块关注（sectionFollow）
  - 版块签到（sectionClock）
- **版主功能**：
  - 设置版主（setModerator）
  - 删除版主（deleteModerator）
  - 帖子审核（postReview）
  - 评论审核（postCommentReview）
- **帖子管理**：
  - 帖子发布（postForum）
  - 帖子编辑（editForum）
  - 帖子删除（postDelete）
  - 帖子置顶（postTop）
  - 帖子推荐（postRecommend）
  - 帖子锁定（postLock）
  - 帖子轮播（postSwiper）
- **互动功能**：
  - 帖子点赞（postLikes）
  - 帖子评论
  - 帖子转发
  - 帖子收藏

#### 2.5.4 商城系统详细功能
- **商品管理**：
  - 商品添加（addShop）
  - 商品编辑（editShop）
  - 商品删除（deleteShop）
  - 商品审核（auditShop）
  - 商品上下架
- **商品分类**：
  - 分类添加（addShopType）
  - 分类编辑（editShopType）
  - 分类删除（deleteShopType）
  - 分类列表（shopTypeList）
- **商品类型**：
  - **实体商品**：需要物流配送
  - **虚拟商品**：自动发货
  - **教程资源**：在线学习
  - **源码下载**：代码资源
  - **VIP套餐**：会员服务
- **购买流程**：
  - 商品浏览（shopList）
  - 商品详情（shopInfo）
  - 立即购买（buyShop）
  - 购买验证（isBuyShop）
  - 订单管理（orderList）

#### 2.5.5 支付系统详细功能
- **支付方式**：
  - 支付宝支付（AliPayStar）
  - 微信支付（WxPayStar）
  - 积分支付
  - 余额支付
- **支付流程**：
  - 订单创建
  - 支付参数生成
  - 第三方支付调用
  - 支付结果回调
  - 订单状态更新
- **支付记录**：
  - 支付日志（paylog）
  - 退款处理
  - 对账功能

#### 2.5.6 消息系统详细功能
- **私信功能**：
  - 发送私信（sendUser）
  - 私信列表（inbox）
  - 未读消息数（unreadNum）
  - 标记已读（setRead）
- **系统通知**：
  - 系统消息推送
  - 公告通知
  - 活动通知
- **推送服务**：
  - APP推送（setClientId）
  - 邮件通知
  - 短信通知（sendSMS）

#### 2.5.7 社交功能详细实现
- **关注系统**：
  - 关注用户（follow）
  - 取消关注
  - 关注状态查询（isFollow）
  - 粉丝列表（fanList）
  - 关注列表（followList）
- **动态系统**：
  - 发布动态（spaceAdd）
  - 动态列表（spaceList）
  - 动态删除（spaceDelete）
  - 动态点赞（spaceLikes）
- **互动功能**：
  - 点赞系统
  - 评论系统
  - 转发功能
  - 收藏功能

## 3. 技术架构

### 3.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用层     │    │   管理端层      │    │   移动端层      │
│                │    │                │    │                │
│  uni-app       │    │  PHP Admin     │    │  H5/APP/小程序  │
│  Vue.js        │    │  Bootstrap     │    │  TuniaoUI      │
│  TuniaoUI      │    │  jQuery        │    │  uView         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API网关层     │
                    │                │
                    │  Spring Boot   │
                    │  RESTful API   │
                    │  JWT认证       │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   业务逻辑层     │
                    │                │
                    │  Service层     │
                    │  业务处理      │
                    │  权限控制      │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据访问层     │
                    │                │
                    │  MyBatis       │
                    │  DAO层         │
                    │  SQL映射       │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MySQL数据库   │    │   Redis缓存     │    │   文件存储      │
│                │    │                │    │                │
│  用户数据      │    │  会话缓存      │    │  图片/视频     │
│  内容数据      │    │  热点数据      │    │  文档附件      │
│  交易数据      │    │  临时数据      │    │  静态资源      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 3.2 技术选型原则
- **稳定性**：选择成熟稳定的技术框架
- **性能**：考虑高并发和大数据量处理
- **扩展性**：支持水平扩展和功能扩展
- **维护性**：代码结构清晰，便于维护

## 4. 技术栈分析

### 4.1 后端技术栈（API）

#### 4.1.1 核心框架
- **Spring Boot 2.2.11.RELEASE**：主框架，提供自动配置和快速开发能力
- **Java 1.8**：开发语言，稳定可靠的LTS版本
- **Maven**：项目管理工具，依赖管理和构建自动化

#### 4.1.2 数据层
- **MySQL 8.0.28**：主数据库，支持JSON字段和更好的性能
- **MyBatis 2.0.0**：ORM框架，灵活的SQL映射
- **Redis 3.1.12**：缓存数据库，高性能内存存储
- **HikariCP**：连接池，高性能数据库连接管理

#### 4.1.3 Web层
- **Spring Web 5.2.20**：Web框架，RESTful API支持
- **Tomcat 9.0.86**：Web容器，内嵌式部署
- **Jackson 2.16.0**：JSON处理，序列化和反序列化

#### 4.1.4 安全与认证
- **JWT**：Token认证，无状态认证机制
- **BCrypt**：密码加密，安全的哈希算法
- **XSS防护**：mica-xss 2.7.14，防止跨站脚本攻击
- **验证码**：Google Kaptcha 2.3.2，图形验证码生成

#### 4.1.5 第三方集成
- **支付宝SDK 4.39.113**：支付集成，支持多种支付方式
- **微信支付SDK 0.4.8**：微信支付，移动端支付首选
- **阿里云OSS 3.16.3**：对象存储，文件存储和CDN
- **腾讯云COS 5.6.65**：对象存储，多云存储支持
- **七牛云SDK 7.15.1**：对象存储，图片处理和存储
- **阿里云短信 2.0.23**：短信服务，验证码和通知
- **通义千问 2.14.8**：AI集成，智能对话功能
- **个推SDK *********：消息推送，APP消息通知

#### 4.1.6 工具库
- **Apache Commons Lang3 3.4**：通用工具类
- **Lombok 1.16.20**：代码生成，减少样板代码
- **ip2region 2.7.0**：IP地址归属地查询
- **JavaCV 1.4.2**：视频处理，封面提取
- **Apache POI 3.17**：Office文档处理

### 4.2 前端技术栈（APP）

#### 4.2.1 核心框架
- **uni-app**：跨平台开发框架，一套代码多端运行
- **Vue.js 2.x**：前端框架，响应式数据绑定
- **Vuex**：状态管理，全局数据管理

#### 4.2.2 UI组件库
- **TuniaoUI**：主要UI组件库，丰富的组件生态
- **uView 1.8.4**：辅助UI组件库，补充组件功能
- **ColorUI**：样式库，美观的UI设计

#### 4.2.3 工具库
- **Prettier 1.12.1**：代码格式化，统一代码风格
- **node-sass 4.14.0**：CSS预处理器，支持Sass语法
- **sass-loader 8.0.2**：Sass加载器，构建工具集成

#### 4.2.4 功能模块
- **lime-painter *********：海报画板，Canvas海报生成
- **buuug7-img-cropper**：图片裁剪，头像上传功能
- **mp-storage**：本地存储，跨平台存储方案
- **x-perm-apply-instr**：权限申请，APP权限管理
- **z-app-permission**：权限检查，权限状态检测

#### 4.2.5 平台适配
- **APP-PLUS**：原生APP功能，5+App特性
- **H5**：Web端适配，浏览器兼容
- **MP-WEIXIN**：微信小程序，小程序特性
- **MP**：其他小程序平台支持

### 4.3 管理端技术栈（Admin）

#### 4.3.1 后端技术
- **PHP 7.x+**：服务端语言
- **MySQL**：数据库
- **Redis**：缓存

#### 4.3.2 前端技术
- **Bootstrap**：UI框架
- **jQuery**：JavaScript库
- **Chart.js**：图表库

## 5. 项目结构

### 5.1 整体目录结构
```
YQA/
├── API/                    # 后端API服务
│   ├── src/               # 源代码
│   ├── target/            # 编译输出
│   ├── pom.xml           # Maven配置
│   └── README.md         # API文档
├── APP/                   # 前端应用
│   ├── pages/            # 页面文件
│   ├── components/       # 组件文件
│   ├── static/           # 静态资源
│   ├── utils/            # 工具类
│   ├── manifest.json     # 应用配置
│   ├── pages.json        # 页面配置
│   └── package.json      # 依赖配置
├── admin/                 # 管理端
│   ├── admin/            # 管理页面
│   ├── Plugins/          # 插件目录
│   ├── Style/            # 样式文件
│   └── index.php         # 入口文件
└── README.md             # 项目说明
```

### 5.2 API项目结构
```
API/src/main/java/com/StarProApi/
├── Application.java              # 启动类
├── annotation/                   # 注解定义
│   └── LoginRequired.java       # 登录验证注解
├── aspect/                       # 切面编程
│   └── LoginAspect.java         # 登录切面
├── common/                       # 公共工具类
│   ├── ApiResult.java           # API响应封装
│   ├── BCrypt.java              # 密码加密
│   ├── HttpUtils.java           # HTTP工具
│   └── ResultAll.java           # 结果封装
├── config/                       # 配置类
│   └── KaptchaConfig.java       # 验证码配置
├── dao/                          # 数据访问层
├── entity/                       # 实体类
├── service/                      # 业务逻辑层
│   └── impl/                    # 实现类
└── web/                          # 控制器层

### 5.3 APP项目结构
```
APP/
├── App.vue                       # 应用入口组件
├── main.js                       # 应用入口文件
├── manifest.json                 # 应用配置文件
├── pages.json                    # 页面路由配置
├── uni.scss                      # 全局样式
├── pages/                        # 页面目录
│   ├── home/                    # 首页
│   ├── user/                    # 用户相关页面
│   ├── contents/                # 内容相关页面
│   ├── forum/                   # 论坛相关页面
│   ├── shop/                    # 商城相关页面
│   ├── manage/                  # 管理相关页面
│   ├── tabPage/                 # 标签页组件
│   └── components/              # 公共组件
├── static/                       # 静态资源
│   ├── index.js                 # Vuex状态管理
│   ├── images/                  # 图片资源
│   └── styles/                  # 样式文件
├── utils/                        # 工具类
│   ├── api.js                   # API接口定义
│   ├── net.js                   # 网络请求封装
│   ├── darkMode.js              # 夜间模式
│   └── pageConfig.js            # 页面配置
├── tuniao-ui/                    # UI组件库
└── uni_modules/                  # uni-app模块
```

## 6. API接口文档

### 6.1 接口规范

#### 6.1.1 请求格式
- **协议**：HTTP/HTTPS
- **方法**：GET、POST、PUT、DELETE
- **编码**：UTF-8
- **格式**：JSON

#### 5.1.2 响应格式
```json
{
  "code": 1,           // 状态码：1成功，0失败
  "msg": "操作成功",    // 响应消息
  "data": {},          // 响应数据
  "time": "2024-01-01T12:00:00"  // 响应时间
}
```

#### 5.1.3 认证机制
- **Token认证**：使用JWT Token进行用户认证
- **请求头**：Authorization: Bearer {token}
- **Token有效期**：24小时（可配置）

### 5.2 核心接口模块

#### 5.2.1 用户认证模块（/SProUsers）

**用户登录**
```
POST /SProUsers/userLogin
参数：
- params: JSON字符串，包含用户名和密码
响应：用户信息和Token
```

**用户注册**
```
POST /SProUsers/userRegister
参数：
- params: JSON字符串，包含注册信息
响应：注册结果
```

**手机号登录**
```
POST /SProUsers/phoneLogin
参数：
- phone: 手机号
- code: 验证码
响应：用户信息和Token
```

#### 5.2.2 内容管理模块（/SProContents）

**文章列表**
```
GET /SProContents/contentsList
参数：
- page: 页码
- limit: 每页数量
- searchKey: 搜索关键词
- order: 排序方式
响应：文章列表
```

**文章详情**
```
GET /SProContents/contentsInfo
参数：
- key: 文章ID
- token: 用户Token（可选）
响应：文章详细信息
```

**发布文章**
```
POST /SProContents/contentsAdd
参数：
- params: 文章信息JSON
- text: 文章内容
- token: 用户Token
响应：发布结果
```

#### 5.2.3 论坛模块（/SProForum）

**帖子列表**
```
GET /SProForum/postsList
参数：
- page: 页码
- limit: 每页数量
- section: 版块ID
- order: 排序方式
响应：帖子列表
```

**发布帖子**
```
POST /SProForum/postsAdd
参数：
- params: 帖子信息JSON
- token: 用户Token
响应：发布结果
```

#### 5.2.4 商城模块（/SProShop）

**商品列表**
```
GET /SProShop/shopList
参数：
- page: 页码
- limit: 每页数量
- type: 商品类型
响应：商品列表
```

**购买商品**
```
POST /SProShop/buyShop
参数：
- shopId: 商品ID
- token: 用户Token
响应：购买结果
```

#### 5.2.5 支付模块（/Pay）

**支付宝支付**
```
POST /Pay/AliPayStar
参数：
- price: 支付金额
- token: 用户Token
响应：支付参数
```

**微信支付**
```
POST /Pay/WxPayStar
参数：
- price: 支付金额
- token: 用户Token
响应：支付参数
```

### 5.3 权限控制

#### 5.3.1 权限级别
- **-2**：无需登录
- **-1**：需要登录
- **0**：普通用户
- **1**：VIP用户
- **2**：管理员

#### 5.3.2 权限验证
使用`@LoginRequired`注解进行权限控制：
```java
@LoginRequired(purview = "0")  // 需要普通用户权限
@LoginRequired(purview = "1")  // 需要VIP权限
@LoginRequired(purview = "2")  // 需要管理员权限
```

## 7. 数据库设计

### 7.1 核心数据表

#### 6.1.1 用户表（typecho_users）
```sql
CREATE TABLE `typecho_users` (
  `uid` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(32) NOT NULL,
  `password` varchar(64) NOT NULL,
  `mail` varchar(200) NOT NULL,
  `url` varchar(200) DEFAULT NULL,
  `screenName` varchar(32) NOT NULL,
  `created` int(10) unsigned NOT NULL,
  `activated` int(10) unsigned NOT NULL,
  `logged` int(10) unsigned NOT NULL,
  `group` varchar(16) NOT NULL DEFAULT 'visitor',
  `authCode` varchar(64) DEFAULT NULL,
  `introduce` text,
  `assets` int(10) DEFAULT '0',
  `experience` int(10) DEFAULT '0',
  `avatar` varchar(255) DEFAULT NULL,
  `vip` int(10) DEFAULT '0',
  `phone` varchar(20) DEFAULT NULL,
  `points` int(10) DEFAULT '0',
  PRIMARY KEY (`uid`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `mail` (`mail`)
);
```

#### 6.1.2 内容表（typecho_contents）
```sql
CREATE TABLE `typecho_contents` (
  `cid` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `slug` varchar(200) DEFAULT NULL,
  `created` int(10) unsigned NOT NULL,
  `modified` int(10) unsigned NOT NULL,
  `text` longtext NOT NULL,
  `order` int(10) unsigned NOT NULL DEFAULT '0',
  `authorId` int(10) unsigned NOT NULL,
  `template` varchar(32) DEFAULT NULL,
  `type` varchar(16) NOT NULL DEFAULT 'post',
  `status` varchar(16) NOT NULL DEFAULT 'publish',
  `password` varchar(32) DEFAULT NULL,
  `commentsNum` int(10) unsigned NOT NULL DEFAULT '0',
  `allowComment` char(1) NOT NULL DEFAULT '0',
  `allowPing` char(1) NOT NULL DEFAULT '0',
  `allowFeed` char(1) NOT NULL DEFAULT '0',
  `parent` int(10) unsigned NOT NULL DEFAULT '0',
  `views` int(10) DEFAULT '0',
  `likes` int(10) DEFAULT '0',
  PRIMARY KEY (`cid`)
);
```

#### 6.1.3 论坛表（typecho_forum）
```sql
CREATE TABLE `typecho_forum` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `text` longtext NOT NULL,
  `authorId` int(11) NOT NULL,
  `created` int(11) NOT NULL,
  `modified` int(11) NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'publish',
  `views` int(11) DEFAULT '0',
  `likes` int(11) DEFAULT '0',
  `comments` int(11) DEFAULT '0',
  `section` int(11) DEFAULT '0',
  `isTop` int(1) DEFAULT '0',
  PRIMARY KEY (`id`)
);
```

#### 6.1.4 商品表（typecho_shop）
```sql
CREATE TABLE `typecho_shop` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `imgurl` varchar(500) DEFAULT NULL,
  `text` longtext,
  `price` int(11) DEFAULT '0',
  `integral` int(11) DEFAULT '0',
  `num` int(11) DEFAULT '0',
  `type` int(11) DEFAULT '0',
  `value` longtext,
  `cid` int(11) DEFAULT '0',
  `uid` int(11) DEFAULT '0',
  `created` int(11) DEFAULT '0',
  `status` int(1) DEFAULT '0',
  PRIMARY KEY (`id`)
);
```

### 6.2 数据表关系

#### 6.2.1 实体关系图
```
用户表(users) 1:N 内容表(contents)
用户表(users) 1:N 论坛表(forum)
用户表(users) 1:N 商品表(shop)
内容表(contents) 1:N 评论表(comments)
论坛表(forum) 1:N 评论表(comments)
内容表(contents) N:M 分类表(metas) [通过关系表relationships]
```

#### 6.2.2 索引设计
- **主键索引**：所有表的主键
- **唯一索引**：用户名、邮箱
- **普通索引**：创建时间、状态、作者ID
- **复合索引**：状态+创建时间、作者ID+状态

## 8. 前端架构

### 8.1 页面结构

#### 7.1.1 主要页面模块
- **首页（home）**：内容展示、轮播图、快捷入口
- **发现（find）**：内容发现、推荐算法
- **消息（msg）**：私信、通知、系统消息
- **用户（user）**：个人中心、设置、资料

#### 7.1.2 页面路由配置
```json
{
  "pages": [
    {
      "path": "pages/home/<USER>",
      "style": {
        "enablePullDownRefresh": true
      }
    }
  ],
  "subPackages": [
    {
      "root": "pages/user/",
      "name": "user",
      "pages": [
        {
          "path": "login",
          "style": {
            "navigationBarTitleText": "登录"
          }
        }
      ]
    }
  ]
}
```

### 7.2 组件架构

#### 7.2.1 组件分类
- **页面组件**：完整的页面级组件
- **业务组件**：特定业务逻辑的组件
- **基础组件**：通用的UI组件

#### 7.2.2 核心组件
```javascript
// 主要页面组件
import home from './pages/tabPage/home.vue'
import find from './pages/tabPage/find.vue'
import msg from './pages/tabPage/msg.vue'
import user from './pages/tabPage/user.vue'

// 业务组件
import forumIndex from './pages/components/forumIndex.vue'
import articleItem from './pages/components/articleItem.vue'
import commentItem from './pages/components/commentItem.vue'
```

### 7.3 状态管理

#### 7.3.1 Vuex Store结构
```javascript
const store = new Vuex.Store({
  state: {
    AppStyle: appStyle,  // 应用样式
    userInfo: {},        // 用户信息
    isDark: false        // 夜间模式
  },
  mutations: {
    setStyle(state, style) {
      state.AppStyle = style
    },
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo
    }
  }
})
```

#### 7.3.2 本地存储
- **用户信息**：localStorage存储用户基本信息
- **Token**：localStorage存储认证Token
- **应用配置**：localStorage存储应用配置信息

### 7.4 网络请求

#### 7.4.1 API封装
```javascript
// API接口定义
const API = {
  userLogin: function() {
    return API_URL + 'SProUsers/userLogin';
  },
  contentsList: function() {
    return API_URL + 'SProContents/contentsList';
  }
}

// 网络请求封装
function request(requestHandler) {
  var data = requestHandler.data || {};
  if (localStorage.getItem('token')) {
    data.token = localStorage.getItem('token');
  }
  uni.request({
    url: requestHandler.url,
    data: data,
    method: requestHandler.method,
    success: requestHandler.success,
    fail: requestHandler.fail
  })
}
```

## 9. 后端架构

### 9.1 分层架构

#### 8.1.1 控制器层（Controller）
负责接收HTTP请求，参数验证，调用业务逻辑层：
```java
@Controller
@RequestMapping(value = "/SProUsers")
public class TypechoUsersController {

    @Autowired
    TypechoUsersService service;

    @RequestMapping(value = "/userLogin")
    @ResponseBody
    @LoginRequired(purview = "-2")
    public String userLogin(@RequestParam String params) {
        // 业务逻辑处理
        return Result.getResultJson(1, "登录成功", data);
    }
}
```

#### 8.1.2 服务层（Service）
实现具体的业务逻辑：
```java
@Service
public class TypechoUsersServiceImpl implements TypechoUsersService {

    @Autowired
    TypechoUsersDao dao;

    @Override
    public List<TypechoUsers> selectList(TypechoUsers users) {
        return dao.selectList(users);
    }
}
```

#### 8.1.3 数据访问层（DAO）
负责数据库操作：
```java
@Mapper
public interface TypechoUsersDao {
    List<TypechoUsers> selectList(TypechoUsers users);
    int insert(TypechoUsers users);
    int update(TypechoUsers users);
    int delete(Object key);
}
```

### 8.2 核心功能模块

#### 8.2.1 用户认证流程
1. **登录验证**：验证用户名密码
2. **Token生成**：生成JWT Token
3. **Redis缓存**：将用户信息缓存到Redis
4. **权限验证**：基于注解的权限控制

#### 8.2.2 内容管理流程
1. **内容发布**：支持富文本、Markdown
2. **内容审核**：自动审核和人工审核
3. **内容分类**：支持多级分类和标签
4. **内容搜索**：基于关键词的全文搜索

#### 8.2.3 支付处理流程
1. **订单创建**：生成唯一订单号
2. **支付调用**：调用第三方支付接口
3. **回调处理**：处理支付结果回调
4. **订单更新**：更新订单状态和用户资产

### 8.3 安全机制

#### 8.3.1 认证安全
- **密码加密**：使用BCrypt加密存储
- **Token机制**：JWT Token认证
- **会话管理**：Redis存储会话信息

#### 8.3.2 接口安全
- **参数验证**：严格的参数类型和格式验证
- **XSS防护**：输入内容XSS过滤
- **SQL注入防护**：使用预编译语句
- **频率限制**：防止恶意请求攻击

## 10. 管理端功能

### 10.1 功能模块

#### 9.1.1 系统管理
- **全局设置**：网站基本信息配置
- **用户设置**：用户相关参数配置
- **首页设置**：首页布局和内容配置
- **支付设置**：支付方式和参数配置

#### 9.1.2 内容管理
- **文章管理**：文章的增删改查
- **论坛管理**：帖子和版块管理
- **评论管理**：评论审核和管理
- **分类管理**：内容分类和标签管理

#### 9.1.3 用户管理
- **用户列表**：用户信息查看和编辑
- **权限管理**：用户组和权限设置
- **资产管理**：用户积分和VIP管理

#### 9.1.4 商城管理
- **商品管理**：商品的增删改查
- **订单管理**：订单查看和处理
- **分类管理**：商品分类管理

### 9.2 技术实现

#### 9.2.1 后端架构
```php
// 数据库连接
$connect = mysqli_connect($db_address, $db_username, $db_password, $db_name);

// Redis连接
$connectRedis = new Redis();
$connectRedis->connect($redis_host, $redis_port);

// 权限验证
if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
    // 管理员操作
} else {
    // 跳转到登录页面
}
```

#### 9.2.2 前端界面
- **Bootstrap框架**：响应式布局
- **jQuery**：DOM操作和AJAX请求
- **Chart.js**：数据图表展示
- **富文本编辑器**：内容编辑功能

## 11. 部署配置

### 11.1 环境要求

#### 10.1.1 服务器环境
- **操作系统**：Linux/Windows
- **Java版本**：JDK 1.8+
- **PHP版本**：PHP 7.0+
- **Web服务器**：Nginx/Apache

#### 10.1.2 数据库环境
- **MySQL**：5.7+或8.0+
- **Redis**：5.0+

### 10.2 配置文件

#### 10.2.1 API配置（application.properties）
```properties
# 服务器配置
server.port=7979
server.address=0.0.0.0

# 数据库配置
spring.datasource.url=***********************************************************
spring.datasource.username=root
spring.datasource.password=root

# Redis配置
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=

# 应用配置
webinfo.key=123456
webinfo.usertime=86400
```

#### 10.2.2 管理端配置（Config_DB.php）
```php
<?php
// API站点地址
$api_site = "http://127.0.0.1:7979/";
$api_key = "123456";

// 数据库配置
$db_address = "127.0.0.1";
$db_username = "root";
$db_name = "yanquan";
$db_password = "root";

// Redis配置
$redis_host = '127.0.0.1';
$redis_port = 6379;
$redis_password = '';
?>
```

### 10.3 部署步骤

#### 10.3.1 API部署
1. **编译打包**：`mvn clean package`
2. **上传文件**：将jar包上传到服务器
3. **启动服务**：`java -jar StarProApi-2.6.jar`
4. **配置反向代理**：Nginx配置API代理

#### 10.3.2 前端部署
1. **编译构建**：使用HBuilderX编译
2. **上传文件**：将编译后的文件上传到Web服务器
3. **配置域名**：配置域名和SSL证书

#### 10.3.3 管理端部署
1. **上传文件**：将PHP文件上传到Web服务器
2. **配置权限**：设置文件读写权限
3. **配置数据库**：修改数据库连接配置

## 12. 二次开发指南

### 12.1 开发环境搭建

#### 11.1.1 后端开发环境
- **IDE**：IntelliJ IDEA或Eclipse
- **JDK**：Oracle JDK 1.8或OpenJDK 1.8
- **Maven**：3.6+
- **数据库**：MySQL 8.0+、Redis 5.0+

#### 11.1.2 前端开发环境
- **IDE**：HBuilderX或VSCode
- **Node.js**：14.0+
- **npm**：6.0+

### 11.2 开发规范

#### 11.2.1 代码规范
- **命名规范**：驼峰命名法
- **注释规范**：类和方法必须有注释
- **异常处理**：统一的异常处理机制
- **日志规范**：使用统一的日志格式

#### 11.2.2 数据库规范
- **表命名**：使用前缀typecho_
- **字段命名**：使用驼峰命名法
- **索引设计**：合理设计索引提高查询性能

### 11.3 扩展开发

#### 11.3.1 API接口扩展
1. **创建Controller**：继承基础Controller
2. **实现Service**：实现业务逻辑
3. **配置权限**：使用@LoginRequired注解
4. **测试接口**：编写单元测试

#### 11.3.2 前端页面扩展
1. **创建页面**：在pages目录下创建新页面
2. **配置路由**：在pages.json中配置路由
3. **实现逻辑**：使用Vue.js实现页面逻辑
4. **样式设计**：使用TuniaoUI组件

#### 11.3.3 插件开发
1. **插件结构**：按照插件规范创建目录结构
2. **配置文件**：创建config.ini配置文件
3. **实现功能**：实现插件核心功能
4. **注册插件**：在管理端注册插件

### 11.4 性能优化

#### 11.4.1 后端优化
- **数据库优化**：索引优化、查询优化
- **缓存策略**：Redis缓存热点数据
- **连接池优化**：合理配置数据库连接池
- **异步处理**：使用异步处理耗时操作

#### 11.4.2 前端优化
- **代码分割**：使用分包加载
- **图片优化**：压缩图片、使用WebP格式
- **缓存策略**：合理使用浏览器缓存
- **懒加载**：实现图片和组件懒加载

### 11.5 常见问题

#### 11.5.1 开发问题
- **跨域问题**：配置CORS或使用代理
- **Token失效**：实现Token自动刷新机制
- **图片上传**：配置文件上传大小限制
- **支付回调**：确保回调地址可访问

#### 11.5.2 部署问题
- **端口冲突**：修改应用端口配置
- **数据库连接**：检查数据库连接配置
- **文件权限**：设置正确的文件读写权限
- **SSL证书**：配置HTTPS证书

---

## 总结

YQA项目是一个功能完善的社区平台，采用现代化的技术架构，具有良好的扩展性和维护性。本文档详细介绍了项目的技术架构、开发规范和部署方式，为二次开发提供了全面的指导。

在进行二次开发时，建议：
1. 熟悉项目整体架构和技术栈
2. 遵循项目的开发规范和编码标准
3. 充分利用现有的组件和工具类
4. 注意安全性和性能优化
5. 完善测试和文档

希望本文档能够帮助开发者快速理解和使用YQA项目，实现高效的二次开发。

## 13. 插件系统

### 13.1 插件架构

#### 13.1.1 插件目录结构
```
Plugins/
├── Load_Plugin.php           # 插件加载器
├── Load_FreePlugin.php       # 免费插件加载
├── config.php               # 插件配置
└── sy_starpro/              # 插件示例
    ├── api.php              # 插件API接口
    ├── config.php           # 插件配置
    ├── db.php              # 数据库操作
    └── set.php             # 插件设置
```

#### 13.1.2 插件类型
- **系统插件**：内置的核心功能插件
- **应用插件**：独立的应用功能模块
- **主题插件**：UI主题和样式插件
- **工具插件**：辅助工具类插件

#### 13.1.3 插件生命周期
1. **插件注册**：在管理端注册插件
2. **插件安装**：执行安装脚本，创建数据表
3. **插件配置**：设置插件参数
4. **插件运行**：插件功能执行
5. **插件卸载**：清理数据和配置

### 13.2 插件开发

#### 13.2.1 插件配置文件
```ini
[plugin]
name=插件名称
version=1.0.0
author=作者
description=插件描述
api_version=2.6
```

#### 13.2.2 插件接口
```php
// 插件必须实现的接口
interface PluginInterface {
    public function install();     // 安装方法
    public function uninstall();   // 卸载方法
    public function activate();    // 激活方法
    public function deactivate();  // 停用方法
    public function config();      // 配置方法
}
```

#### 13.2.3 插件钩子
- **before_content_save**：内容保存前
- **after_content_save**：内容保存后
- **before_user_login**：用户登录前
- **after_user_login**：用户登录后
- **before_payment**：支付前
- **after_payment**：支付后

### 13.3 内置插件

#### 13.3.1 GPT插件（sy_gpt）
- **功能**：集成GPT对话功能
- **技术**：阿里通义千问API
- **特点**：支持多轮对话、历史记录

#### 13.3.2 应用盒子插件（sy_appbox）
- **功能**：应用展示和下载
- **特点**：支持多种应用类型、评分系统

## 14. 安全机制

### 14.1 认证与授权

#### 14.1.1 JWT Token机制
```java
// Token生成
String token = JWT.create()
    .withClaim("uid", user.getUid())
    .withClaim("time", System.currentTimeMillis())
    .withExpiresAt(expiresAt)
    .sign(algorithm);

// Token验证
@LoginRequired(purview = "0")
public String api() {
    // 需要登录的API
}
```

#### 14.1.2 权限等级
- **-2**：游客（无需登录）
- **-1**：需要登录
- **0**：普通用户
- **1**：VIP用户
- **2**：管理员

### 14.2 数据安全

#### 14.2.1 密码安全
- **加密算法**：BCrypt
- **加密强度**：10轮迭代
- **密码策略**：强制复杂密码

#### 14.2.2 SQL注入防护
- **MyBatis参数化查询**
- **输入参数验证**
- **预编译语句**

#### 14.2.3 XSS防护
```java
// 使用mica-xss进行内容过滤
@Configuration
public class XssConfig {
    @Bean
    public XssFilter xssFilter() {
        return new XssFilter();
    }
}
```

### 14.3 接口安全

#### 14.3.1 频率限制
- **IP限制**：单IP请求频率限制
- **用户限制**：单用户操作频率限制
- **黑名单机制**：恶意IP自动封禁

#### 14.3.2 验证码机制
- **Google Kaptcha**：图形验证码
- **短信验证码**：手机号验证
- **邮箱验证码**：邮箱验证

#### 14.3.3 内容审核
- **敏感词过滤**：基于词库的过滤
- **腾讯云内容安全**：图片、文本审核
- **人工审核**：高风险内容人工复审

### 14.4 文件安全

#### 14.4.1 上传限制
- **文件类型**：白名单机制
- **文件大小**：分级限制
- **文件扫描**：恶意代码检测

#### 14.4.2 存储安全
- **对象存储**：支持阿里云OSS、腾讯云COS、七牛云
- **访问控制**：基于Token的访问控制
- **防盗链**：Referer验证

## 15. 性能优化

### 15.1 后端优化

#### 15.1.1 数据库优化
```sql
-- 索引优化示例
CREATE INDEX idx_contents_author_status ON typecho_contents(authorId, status);
CREATE INDEX idx_forum_section_created ON typecho_forum(section, created);
CREATE INDEX idx_comments_cid_created ON typecho_comments(cid, created);
```

#### 15.1.2 缓存策略
```java
// Redis缓存配置
@Configuration
public class RedisConfig {
    // 缓存时间配置
    public static final int USER_CACHE_TIME = 3600;      // 用户信息缓存1小时
    public static final int CONTENT_CACHE_TIME = 600;    // 内容缓存10分钟
    public static final int HOT_DATA_CACHE_TIME = 300;   // 热点数据缓存5分钟
}
```

#### 15.1.3 异步处理
- **消息推送**：异步发送
- **邮件发送**：队列处理
- **图片处理**：后台任务

#### 15.1.4 连接池优化
```properties
# HikariCP连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
```

### 15.2 前端优化

#### 15.2.1 代码分包
```json
// manifest.json分包配置
{
  "optimization": {
    "subPackages": true
  },
  "subPackages": [
    {
      "root": "pages/user/",
      "name": "user",
      "pages": ["login", "register"]
    }
  ]
}
```

#### 15.2.2 图片优化
- **懒加载**：使用IntersectionObserver
- **格式优化**：WebP格式支持
- **CDN加速**：静态资源CDN
- **压缩处理**：自动压缩上传图片

#### 15.2.3 网络优化
- **请求合并**：批量请求接口
- **缓存策略**：本地存储缓存
- **预加载**：提前加载关键资源

### 15.3 架构优化

#### 15.3.1 微服务拆分建议
- **用户服务**：用户认证、权限管理
- **内容服务**：文章、论坛、评论
- **交易服务**：商城、支付、订单
- **消息服务**：通知、推送、聊天

#### 15.3.2 部署优化
- **负载均衡**：Nginx反向代理
- **容器化**：Docker部署
- **集群部署**：Redis集群、MySQL主从

### 15.4 监控与分析

#### 15.4.1 性能监控
- **APM工具**：应用性能监控
- **日志分析**：ELK日志系统
- **数据库监控**：慢查询分析

#### 15.4.2 用户行为分析
- **埋点统计**：关键操作埋点
- **漏斗分析**：转化率分析
- **热力图**：页面热区分析

---

## 总结

YQA/StarPro项目是一个功能完善、技术先进的社区平台解决方案。项目采用主流技术栈，具有以下特点：

1. **完整的功能体系**：覆盖社区运营的各个方面
2. **灵活的架构设计**：支持水平扩展和功能扩展
3. **全面的安全防护**：多层次的安全机制
4. **优秀的性能表现**：经过优化的高性能架构
5. **便捷的开发体验**：完善的开发文档和规范

本文档详细介绍了项目的技术架构、功能实现、部署配置和优化方案，为开发者提供全面的技术指导。在进行二次开发时，建议：

1. **深入理解架构**：熟悉项目整体架构和设计理念
2. **遵循开发规范**：保持代码风格和质量的一致性
3. **重视安全防护**：严格执行安全策略和最佳实践
4. **持续性能优化**：定期进行性能分析和优化
5. **完善测试文档**：编写充分的测试用例和文档

希望本文档能够帮助开发者快速掌握YQA/StarPro项目，实现高效的开发和运营。

## 16. 核心变量和函数

### 16.1 全局配置变量

#### 16.1.1 API配置变量（application.properties）
```properties
# 服务器配置
server.port=7979                    # API服务端口
web.prefix=typecho                  # Redis缓存前缀
webinfo.key=123456                  # API密钥
webinfo.usertime=86400              # 用户Token有效期（秒）

# 缓存配置
webinfo.contentCache=6              # 内容缓存时间（分钟）
webinfo.contentInfoCache=60         # 内容详情缓存时间（分钟）
webinfo.CommentCache=20             # 评论缓存时间（分钟）
webinfo.userCache=10                # 用户缓存时间（分钟）

# 数据库配置
mybatis.configuration.variables.prefix=typecho  # 数据库表前缀

# 文件上传配置
spring.servlet.multipart.max-file-size=20MB     # 单文件大小限制
spring.servlet.multipart.max-request-size=100MB # 请求大小限制

# XSS防护配置
mica.xss.allow-tags=p,audio,video              # 允许的HTML标签
mica.xss.allow-attributes=src,controls,id      # 允许的HTML属性
```

#### 16.1.2 APP配置变量（api.js）
```javascript
// 核心配置
var API_URL = '#';                  // StarApi域名
var STAR_URL = '#';                 // Star后台域名
var WEB_URL = '#';                  // H5或网页端域名
var appKey = "#";                   // APP密钥

// 基本配置
var userID = "UID";                 // 用户标识
var appEmail = "<EMAIL>";        // 站长邮箱
var appName = "XX社区";             // 社区名称
var appLogo = "../../static/icon.png"; // 社区LOGO
var company = "XX工作室";           // 运营主体

// 功能配置
var isShowTop = true;               // 全局置顶是否合并显示
var homeSwiperHeight = 300;         // 首页轮播图高度
var forumSwiperHeight = 300;        // 圈子页轮播图高度
var findSwiperHeight = 300;         // 发现页轮播图高度
var isLocalPic = 1;                 // VIP等级图片本地化开关
```

### 16.2 权限控制变量

#### 16.2.1 权限等级定义
```java
// 权限等级常量
public static final String PURVIEW_NO_LOGIN = "-2";      // 无需登录
public static final String PURVIEW_NEED_LOGIN = "-1";    // 需要登录
public static final String PURVIEW_NORMAL_USER = "0";    // 普通用户
public static final String PURVIEW_VIP_USER = "1";       // VIP用户
public static final String PURVIEW_ADMIN = "2";          // 管理员

// 用户组定义
public static final String GROUP_VISITOR = "visitor";         // 游客
public static final String GROUP_SUBSCRIBER = "subscriber";   // 订阅者
public static final String GROUP_CONTRIBUTOR = "contributor"; // 贡献者
public static final String GROUP_EDITOR = "editor";           // 编辑
public static final String GROUP_ADMINISTRATOR = "administrator"; // 管理员
```

#### 16.2.2 版主权限等级
```java
// 版主权限等级
public static final Integer MODERATOR_LEVEL_0 = 0;  // 无权限
public static final Integer MODERATOR_LEVEL_1 = 1;  // 删帖权限
public static final Integer MODERATOR_LEVEL_2 = 2;  // 置顶权限
public static final Integer MODERATOR_LEVEL_3 = 3;  // 推荐权限
public static final Integer MODERATOR_LEVEL_4 = 4;  // 版主权限
public static final Integer MODERATOR_LEVEL_5 = 5;  // 大版主权限
```

### 16.3 核心函数分析

#### 16.3.1 用户认证函数
```java
// 用户状态检查
public static Integer getStatus(String token, String dataprefix, RedisTemplate redisTemplate)

// 用户权限验证
public static Integer forumPermissions(Map UserMap, TypechoForumModeratorService forumModeratorService,
                                     Integer permissions, Integer sectionId)

// 实名认证检查
public static Integer isIdentifysm(Integer uid, String prefix, JdbcTemplate jdbcTemplate)

// 用户组权限检查
public static Integer getUserPurview(String group)
```

#### 16.3.2 缓存操作函数
```java
// Redis缓存设置
public void setKey(String key, Map<String, Object> map, Integer time, RedisTemplate redisTemplate)

// Redis字符串缓存
public void setRedis(String key, String value, Integer time, RedisTemplate redisTemplate)

// 获取Redis缓存
public String getRedis(String key, RedisTemplate redisTemplate)

// 获取Redis Map
public Map<Object, Object> getMapValue(String key, RedisTemplate redisTemplate)

// 缓存列表数据
public <T> long setList(final String key, final List<T> dataList, final Integer time, RedisTemplate redisTemplate)
```

#### 16.3.3 前端工具函数
```javascript
// 网络请求封装
function request(requestHandler) {
    // 自动添加Token
    if (localStorage.getItem('token') && !localStorage.getItem('isbug')) {
        data.token = localStorage.getItem('token');
    }
    // 发送请求
    uni.request({...});
}

// 夜间模式检测
function checkDarkMode() {
    let showStyle = uni.getStorageSync('showStyle') || 0;
    // 根据设置返回夜间模式状态
}

// 权限申请
const requestAppPermission = async (options) => {
    const pid = 'android.permission.' + options.permissionID;
    // 检查和申请权限
}
```

### 16.4 数据映射关系

#### 16.4.1 用户数据映射
```java
// 用户实体类主要字段
public class TypechoUsers {
    private Integer uid;           // 用户ID
    private String name;           // 用户名
    private String password;       // 密码（加密）
    private String mail;           // 邮箱
    private String screenName;     // 显示名称
    private String group;          // 用户组
    private Integer assets;        // 资产（积分）
    private Integer experience;    // 经验值
    private String avatar;         // 头像
    private Integer vip;           // VIP状态
    private String phone;          // 手机号
    private Integer points;        // 积分
}
```

#### 16.4.2 内容数据映射
```java
// 内容实体类主要字段
public class TypechoContents {
    private Integer cid;           // 内容ID
    private String title;          // 标题
    private String slug;           // 别名
    private Integer created;       // 创建时间
    private Integer modified;      // 修改时间
    private String text;           // 内容
    private Integer authorId;      // 作者ID
    private String type;           // 类型（post/page）
    private String status;         // 状态（publish/draft）
    private Integer commentsNum;   // 评论数
    private Integer views;         // 浏览量
    private Integer likes;         // 点赞数
}
```

#### 16.4.3 前后端数据交互
```javascript
// API响应格式
{
    "code": 1,              // 状态码：1成功，0失败
    "msg": "操作成功",       // 响应消息
    "data": {},             // 响应数据
    "time": "2024-01-01T12:00:00"  // 响应时间
}

// 用户登录请求
{
    "params": JSON.stringify({
        "name": "username",     // 用户名
        "password": "password", // 密码
        "code": "1234"         // 验证码
    })
}

// 内容发布请求
{
    "params": JSON.stringify({
        "title": "标题",
        "type": "post",
        "status": "publish"
    }),
    "text": "内容正文",
    "token": "用户Token"
}
```

### 16.5 缓存键值规范

#### 16.5.1 用户相关缓存
```
{prefix}_userInfo{token}        # 用户信息缓存
{prefix}_userList_{page}        # 用户列表缓存
{prefix}_{uid}_silence          # 用户禁言状态
{prefix}_{ip}_verifyCode        # 验证码缓存
{prefix}_userPurview_{uid}      # 用户权限缓存
```

#### 16.5.2 内容相关缓存
```
{prefix}_contentsList_{page}_{searchKey}  # 内容列表缓存
{prefix}_contentsInfo_{cid}               # 内容详情缓存
{prefix}_comments_{cid}_{page}            # 评论列表缓存
{prefix}_hotContents                      # 热门内容缓存
```

#### 16.5.3 论坛相关缓存
```
{prefix}_postsList_{section}_{page}       # 帖子列表缓存
{prefix}_postInfo_{id}                    # 帖子详情缓存
{prefix}_sectionList                      # 版块列表缓存
{prefix}_forumModerator_{uid}             # 版主权限缓存
```

## 17. 开发环境搭建

### 17.1 后端开发环境

#### 17.1.1 必需软件
- **JDK 1.8+**：Oracle JDK或OpenJDK
- **Maven 3.6+**：项目构建工具
- **MySQL 8.0+**：数据库服务
- **Redis 5.0+**：缓存服务
- **IntelliJ IDEA**：推荐IDE

#### 17.1.2 环境配置
```bash
# 1. 安装JDK
java -version

# 2. 安装Maven
mvn -version

# 3. 启动MySQL服务
mysql -u root -p

# 4. 启动Redis服务
redis-server

# 5. 导入数据库
mysql -u root -p yanquan < database.sql
```

### 17.2 前端开发环境

#### 17.2.1 必需软件
- **HBuilderX**：uni-app官方IDE
- **Node.js 14.0+**：JavaScript运行环境
- **npm 6.0+**：包管理工具

#### 17.2.2 项目配置
```bash
# 1. 安装依赖
npm install

# 2. 配置API地址
# 修改 APP/utils/api.js 中的 API_URL

# 3. 运行项目
# H5端
npm run dev:h5

# 微信小程序
npm run dev:mp-weixin

# APP端
npm run dev:app-plus
```

### 17.3 管理端开发环境

#### 17.3.1 必需软件
- **PHP 7.0+**：服务端语言
- **Apache/Nginx**：Web服务器
- **phpMyAdmin**：数据库管理工具（可选）

#### 17.3.2 配置步骤
```bash
# 1. 配置Web服务器
# 将admin目录放置在Web根目录

# 2. 修改数据库配置
# 编辑 admin/admin/Config_DB.php

# 3. 设置文件权限
chmod 755 admin/
chmod 644 admin/admin/*.php

# 4. 访问管理端
http://localhost/admin/
```

## 18. 常见问题解决

### 18.1 部署问题

#### 18.1.1 API服务启动失败
```bash
# 问题：端口被占用
# 解决：修改application.properties中的server.port

# 问题：数据库连接失败
# 解决：检查数据库配置和网络连接

# 问题：Redis连接失败
# 解决：确认Redis服务状态和配置
```

#### 18.1.2 前端编译问题
```bash
# 问题：依赖安装失败
npm cache clean --force
npm install

# 问题：编译报错
# 检查Node.js版本兼容性
# 清理缓存重新编译
```

### 18.2 功能问题

#### 18.2.1 用户登录问题
- **Token失效**：检查Redis缓存和Token有效期配置
- **权限验证失败**：确认用户组和权限配置
- **验证码错误**：检查验证码生成和验证逻辑

#### 18.2.2 文件上传问题
- **文件大小限制**：调整spring.servlet.multipart配置
- **文件类型限制**：检查upload.allowed-extensions配置
- **存储路径问题**：确认文件存储路径权限

### 18.3 性能问题

#### 18.3.1 数据库性能
- **慢查询优化**：添加适当索引
- **连接池配置**：调整HikariCP参数
- **缓存策略**：合理使用Redis缓存

#### 18.3.2 前端性能
- **页面加载慢**：启用分包加载
- **图片加载慢**：使用图片懒加载
- **内存占用高**：优化组件生命周期

---

**文档版本**：v2.6
**更新日期**：2025年1月
**维护团队**：StarPro开发组
